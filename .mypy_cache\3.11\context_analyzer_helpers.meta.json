{"data_mtime": 1754871198, "dep_lines": [7, 8, 9, 10, 11, 12, 15, 16, 1, 1, 1, 1, 1], "dep_prios": [10, 10, 10, 10, 5, 5, 20, 25, 5, 30, 30, 30, 30], "dependencies": ["re", "json", "logging", "mimetypes", "pathlib", "typing", "prompt_templates", "document_database", "builtins", "_frozen_importlib", "abc", "enum", "os"], "hash": "b426e44d9b8287d1977f3b2c5ea6c59090a2b0af", "id": "context_analyzer_helpers", "ignore_all": true, "interface_hash": "cb451a288b9cd7886f5863a930a2c796375048f4", "mtime": 1754871148, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\context_analyzer_helpers.py", "plugin_data": null, "size": 34088, "suppressed": [], "version_id": "1.15.0"}