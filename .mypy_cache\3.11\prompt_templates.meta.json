{"data_mtime": 1754871198, "dep_lines": [7, 8, 9, 10, 11, 13, 14, 453, 1, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 5, 5, 20, 5, 30, 30, 30], "dependencies": ["logging", "re", "typing", "dataclasses", "enum", "models", "document_database", "context_analyzer_helpers", "builtins", "_frozen_importlib", "abc", "typing_extensions"], "hash": "2b7be1d790e47eef379691c6f67f6d4eda1ae3e0", "id": "prompt_templates", "ignore_all": false, "interface_hash": "187585f8baba6c586fda4de263076a68650718b6", "mtime": 1754871271, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\reposense_ai\\prompt_templates.py", "plugin_data": null, "size": 27145, "suppressed": [], "version_id": "1.15.0"}