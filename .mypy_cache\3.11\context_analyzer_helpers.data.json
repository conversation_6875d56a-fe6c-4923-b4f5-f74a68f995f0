{".class": "MypyFile", "_fullname": "context_analyzer_helpers", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ChangeType": {".class": "SymbolTableNode", "cross_ref": "prompt_templates.ChangeType", "kind": "Gdef"}, "ContextAnalyzerHelpers": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers", "name": "ContextAnalyzerHelpers", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "context_analyzer_helpers", "mro": ["context_analyzer_helpers.ContextAnalyzerHelpers", "builtins.object"], "names": {".class": "SymbolTable", "ChangeType": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.ChangeType", "name": "ChangeType", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.__init__", "name": "__init__", "type": null}}, "_clean_and_summarize_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._clean_and_summarize_context", "name": "_clean_and_summarize_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_clean_and_summarize_context of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_changelog_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._extract_changelog_context", "name": "_extract_changelog_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_changelog_context of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_config_file_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._extract_config_file_context", "name": "_extract_config_file_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "filename"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "pathlib.Path", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_config_file_context of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_contributing_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._extract_contributing_context", "name": "_extract_contributing_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_contributing_context of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_file_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._extract_file_context", "name": "_extract_file_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "filename"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "pathlib.Path", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_file_context of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_generic_text_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._extract_generic_text_context", "name": "_extract_generic_text_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "filename"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_generic_text_context of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_html_file_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._extract_html_file_context", "name": "_extract_html_file_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "filename"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "pathlib.Path", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_html_file_context of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_json_file_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._extract_json_file_context", "name": "_extract_json_file_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "filename"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "pathlib.Path", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_json_file_context of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_readme_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._extract_readme_context", "name": "_extract_readme_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "content"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_readme_context of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_section_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "section_names"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._extract_section_content", "name": "_extract_section_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "section_names"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_section_content of ContextAnalyzerHelpers", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_setup_py_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._extract_setup_py_context", "name": "_extract_setup_py_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_setup_py_context of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_text_file_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "filename"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._extract_text_file_context", "name": "_extract_text_file_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "file_path", "filename"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "pathlib.Path", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_text_file_context of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_repository_path": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "document_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._get_repository_path", "name": "_get_repository_path", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document_record"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "document_database.DocumentRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_repository_path of ContextAnalyzerHelpers", "ret_type": {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_merge_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "main_context", "file_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._merge_context", "name": "_merge_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "main_context", "file_context"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_merge_context of ContextAnalyzerHelpers", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_read_file_with_encoding": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers._read_file_with_encoding", "name": "_read_file_with_encoding", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "file_path"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_read_file_with_encoding of ContextAnalyzerHelpers", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "affects_api": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "changed_files", "commit_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.affects_api", "name": "affects_api", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "changed_files", "commit_message"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "affects_api of ContextAnalyzerHelpers", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "affects_configuration": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "changed_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.affects_configuration", "name": "affects_configuration", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "changed_files"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "affects_configuration of ContextAnalyzerHelpers", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "affects_core_logic": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "changed_files", "commit_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.affects_core_logic", "name": "affects_core_logic", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "changed_files", "commit_message"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "affects_core_logic of ContextAnalyzerHelpers", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "affects_database": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "changed_files", "commit_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.affects_database", "name": "affects_database", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "changed_files", "commit_message"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "affects_database of ContextAnalyzerHelpers", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "affects_security": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "changed_files", "commit_message", "diff_content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.affects_security", "name": "affects_security", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "changed_files", "commit_message", "diff_content"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "affects_security of ContextAnalyzerHelpers", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "affects_ui": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "changed_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.affects_ui", "name": "affects_ui", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "changed_files"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "affects_ui of ContextAnalyzerHelpers", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "analyze_change_magnitude": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "diff_content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.analyze_change_magnitude", "name": "analyze_change_magnitude", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "diff_content"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_change_magnitude of ContextAnalyzerHelpers", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.int", "builtins.int"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "critical_patterns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.critical_patterns", "name": "critical_patterns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "detect_change_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "changed_files", "commit_message", "diff_content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.detect_change_type", "name": "detect_change_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "changed_files", "commit_message", "diff_content"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect_change_type of ContextAnalyzerHelpers", "ret_type": "prompt_templates.ChangeType", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detect_programming_languages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "changed_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.detect_programming_languages", "name": "detect_programming_languages", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "changed_files"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect_programming_languages of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detect_project_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "changed_files", "repository_name"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.detect_project_type", "name": "detect_project_type", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "changed_files", "repository_name"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect_project_type of ContextAnalyzerHelpers", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "detect_repository_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "document"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.detect_repository_type", "name": "detect_repository_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "document_database.DocumentRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "detect_repository_type of ContextAnalyzerHelpers", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_changed_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "document", "diff_content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.extract_changed_files", "name": "extract_changed_files", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "document", "diff_content"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "document_database.DocumentRecord", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_changed_files of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "extract_product_documentation_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "document_record"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.extract_product_documentation_context", "name": "extract_product_documentation_context", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "document_record"], "arg_types": ["context_analyzer_helpers.ContextAnalyzerHelpers", "document_database.DocumentRecord"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "extract_product_documentation_context of ContextAnalyzerHelpers", "ret_type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "file_patterns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.file_patterns", "name": "file_patterns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "language_patterns": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.language_patterns", "name": "language_patterns", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "context_analyzer_helpers.ContextAnalyzerHelpers.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "context_analyzer_helpers.ContextAnalyzerHelpers", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DocumentRecord": {".class": "SymbolTableNode", "cross_ref": "document_database.DocumentRecord", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "RiskContext": {".class": "SymbolTableNode", "cross_ref": "prompt_templates.RiskContext", "kind": "Gdef"}, "Set": {".class": "SymbolTableNode", "cross_ref": "typing.Set", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "context_analyzer_helpers.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "context_analyzer_helpers.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "context_analyzer_helpers.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "context_analyzer_helpers.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "context_analyzer_helpers.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "context_analyzer_helpers.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "mimetypes": {".class": "SymbolTableNode", "cross_ref": "mimetypes", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "C:\\home-repos\\reposense_ai\\context_analyzer_helpers.py"}