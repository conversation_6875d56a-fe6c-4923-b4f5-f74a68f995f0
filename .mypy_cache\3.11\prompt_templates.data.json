{".class": "MypyFile", "_fullname": "prompt_templates", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "ChangeContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_templates.ChangeContext", "name": "ChangeContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_templates.ChangeContext", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 47, "name": "change_type", "type": "prompt_templates.ChangeType"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 48, "name": "risk_context", "type": "prompt_templates.RiskContext"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 49, "name": "files_changed", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 50, "name": "lines_added", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 51, "name": "lines_removed", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 54, "name": "repository_name", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 55, "name": "repository_type", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 56, "name": "programming_languages", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 57, "name": "project_type", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 60, "name": "affects_core_logic", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 61, "name": "affects_security", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 62, "name": "affects_user_interface", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 63, "name": "affects_database", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 64, "name": "affects_api", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 65, "name": "affects_configuration", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 68, "name": "author_experience_level", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 69, "name": "similar_changes_count", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 70, "name": "recent_issues_in_area", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 73, "name": "is_pre_release", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 74, "name": "is_hotfix", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 75, "name": "is_maintenance_window", "type": "builtins.bool"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 78, "name": "critical_files_changed", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 79, "name": "test_files_changed", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 80, "name": "config_files_changed", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 83, "name": "product_overview", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 84, "name": "key_features", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 85, "name": "changelog_structure", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 86, "name": "documentation_style", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 87, "name": "project_goals", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 88, "name": "user_audience", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 89, "name": "technical_stack", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": true, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 90, "name": "deployment_info", "type": "builtins.str"}], "frozen": false}, "dataclass_tag": {}}, "module_name": "prompt_templates", "mro": ["prompt_templates.ChangeContext", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "prompt_templates.ChangeContext.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "change_type", "risk_context", "files_changed", "lines_added", "lines_removed", "repository_name", "repository_type", "programming_languages", "project_type", "affects_core_logic", "affects_security", "affects_user_interface", "affects_database", "affects_api", "affects_configuration", "author_experience_level", "similar_changes_count", "recent_issues_in_area", "is_pre_release", "is_hotfix", "is_maintenance_window", "critical_files_changed", "test_files_changed", "config_files_changed", "product_overview", "key_features", "changelog_structure", "documentation_style", "project_goals", "user_audience", "technical_stack", "deployment_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.ChangeContext.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 1, 1, 1, 1], "arg_names": ["self", "change_type", "risk_context", "files_changed", "lines_added", "lines_removed", "repository_name", "repository_type", "programming_languages", "project_type", "affects_core_logic", "affects_security", "affects_user_interface", "affects_database", "affects_api", "affects_configuration", "author_experience_level", "similar_changes_count", "recent_issues_in_area", "is_pre_release", "is_hotfix", "is_maintenance_window", "critical_files_changed", "test_files_changed", "config_files_changed", "product_overview", "key_features", "changelog_structure", "documentation_style", "project_goals", "user_audience", "technical_stack", "deployment_info"], "arg_types": ["prompt_templates.ChangeContext", "prompt_templates.ChangeType", "prompt_templates.RiskContext", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ChangeContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "prompt_templates.ChangeContext.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "change_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "risk_context"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "files_changed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lines_added"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "lines_removed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "repository_name"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "repository_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "programming_languages"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "project_type"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "affects_core_logic"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "affects_security"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "affects_user_interface"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "affects_database"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "affects_api"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "affects_configuration"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "author_experience_level"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "similar_changes_count"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "recent_issues_in_area"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_pre_release"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_hotfix"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "is_maintenance_window"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "critical_files_changed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "test_files_changed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "config_files_changed"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "product_overview"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "key_features"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "changelog_structure"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "documentation_style"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "project_goals"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "user_audience"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "technical_stack"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "deployment_info"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["change_type", "risk_context", "files_changed", "lines_added", "lines_removed", "repository_name", "repository_type", "programming_languages", "project_type", "affects_core_logic", "affects_security", "affects_user_interface", "affects_database", "affects_api", "affects_configuration", "author_experience_level", "similar_changes_count", "recent_issues_in_area", "is_pre_release", "is_hotfix", "is_maintenance_window", "critical_files_changed", "test_files_changed", "config_files_changed", "product_overview", "key_features", "changelog_structure", "documentation_style", "project_goals", "user_audience", "technical_stack", "deployment_info"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "prompt_templates.ChangeContext.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["change_type", "risk_context", "files_changed", "lines_added", "lines_removed", "repository_name", "repository_type", "programming_languages", "project_type", "affects_core_logic", "affects_security", "affects_user_interface", "affects_database", "affects_api", "affects_configuration", "author_experience_level", "similar_changes_count", "recent_issues_in_area", "is_pre_release", "is_hotfix", "is_maintenance_window", "critical_files_changed", "test_files_changed", "config_files_changed", "product_overview", "key_features", "changelog_structure", "documentation_style", "project_goals", "user_audience", "technical_stack", "deployment_info"], "arg_types": ["prompt_templates.ChangeType", "prompt_templates.RiskContext", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ChangeContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "prompt_templates.ChangeContext.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["change_type", "risk_context", "files_changed", "lines_added", "lines_removed", "repository_name", "repository_type", "programming_languages", "project_type", "affects_core_logic", "affects_security", "affects_user_interface", "affects_database", "affects_api", "affects_configuration", "author_experience_level", "similar_changes_count", "recent_issues_in_area", "is_pre_release", "is_hotfix", "is_maintenance_window", "critical_files_changed", "test_files_changed", "config_files_changed", "product_overview", "key_features", "changelog_structure", "documentation_style", "project_goals", "user_audience", "technical_stack", "deployment_info"], "arg_types": ["prompt_templates.ChangeType", "prompt_templates.RiskContext", "builtins.int", "builtins.int", "builtins.int", "builtins.str", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.bool", "builtins.str", "builtins.int", "builtins.int", "builtins.bool", "builtins.bool", "builtins.bool", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str", "builtins.str"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of ChangeContext", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "affects_api": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.affects_api", "name": "affects_api", "type": "builtins.bool"}}, "affects_configuration": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.affects_configuration", "name": "affects_configuration", "type": "builtins.bool"}}, "affects_core_logic": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.affects_core_logic", "name": "affects_core_logic", "type": "builtins.bool"}}, "affects_database": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.affects_database", "name": "affects_database", "type": "builtins.bool"}}, "affects_security": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.affects_security", "name": "affects_security", "type": "builtins.bool"}}, "affects_user_interface": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.affects_user_interface", "name": "affects_user_interface", "type": "builtins.bool"}}, "author_experience_level": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.author_experience_level", "name": "author_experience_level", "type": "builtins.str"}}, "change_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.change_type", "name": "change_type", "type": "prompt_templates.ChangeType"}}, "changelog_structure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "prompt_templates.ChangeContext.changelog_structure", "name": "changelog_structure", "type": "builtins.str"}}, "config_files_changed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.config_files_changed", "name": "config_files_changed", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "critical_files_changed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.critical_files_changed", "name": "critical_files_changed", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "deployment_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "prompt_templates.ChangeContext.deployment_info", "name": "deployment_info", "type": "builtins.str"}}, "documentation_style": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "prompt_templates.ChangeContext.documentation_style", "name": "documentation_style", "type": "builtins.str"}}, "files_changed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.files_changed", "name": "files_changed", "type": "builtins.int"}}, "is_hotfix": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.is_hotfix", "name": "is_hotfix", "type": "builtins.bool"}}, "is_maintenance_window": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.is_maintenance_window", "name": "is_maintenance_window", "type": "builtins.bool"}}, "is_pre_release": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.is_pre_release", "name": "is_pre_release", "type": "builtins.bool"}}, "key_features": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "prompt_templates.ChangeContext.key_features", "name": "key_features", "type": "builtins.str"}}, "lines_added": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.lines_added", "name": "lines_added", "type": "builtins.int"}}, "lines_removed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.lines_removed", "name": "lines_removed", "type": "builtins.int"}}, "product_overview": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "prompt_templates.ChangeContext.product_overview", "name": "product_overview", "type": "builtins.str"}}, "programming_languages": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.programming_languages", "name": "programming_languages", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "project_goals": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "prompt_templates.ChangeContext.project_goals", "name": "project_goals", "type": "builtins.str"}}, "project_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.project_type", "name": "project_type", "type": "builtins.str"}}, "recent_issues_in_area": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.recent_issues_in_area", "name": "recent_issues_in_area", "type": "builtins.int"}}, "repository_name": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.repository_name", "name": "repository_name", "type": "builtins.str"}}, "repository_type": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.repository_type", "name": "repository_type", "type": "builtins.str"}}, "risk_context": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.risk_context", "name": "risk_context", "type": "prompt_templates.RiskContext"}}, "similar_changes_count": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.similar_changes_count", "name": "similar_changes_count", "type": "builtins.int"}}, "technical_stack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "prompt_templates.ChangeContext.technical_stack", "name": "technical_stack", "type": "builtins.str"}}, "test_files_changed": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "prompt_templates.ChangeContext.test_files_changed", "name": "test_files_changed", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "user_audience": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "prompt_templates.ChangeContext.user_audience", "name": "user_audience", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_templates.ChangeContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_templates.ChangeContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChangeType": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_templates.ChangeType", "name": "ChangeType", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "prompt_templates.ChangeType", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "prompt_templates", "mro": ["prompt_templates.ChangeType", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "API": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.API", "name": "API", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "api"}, "type_ref": "builtins.str"}}}, "BUGFIX": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.BUGFIX", "name": "BUGFIX", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "bugfix"}, "type_ref": "builtins.str"}}}, "BUSINESS_LOGIC": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.BUSINESS_LOGIC", "name": "BUSINESS_LOGIC", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "business_logic"}, "type_ref": "builtins.str"}}}, "CONFIGURATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.CONFIGURATION", "name": "CONFIGURATION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "configuration"}, "type_ref": "builtins.str"}}}, "DATABASE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.DATABASE", "name": "DATABASE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "database"}, "type_ref": "builtins.str"}}}, "DOCUMENTATION": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.DOCUMENTATION", "name": "DOCUMENTATION", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "documentation"}, "type_ref": "builtins.str"}}}, "FEATURE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.FEATURE", "name": "FEATURE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "feature"}, "type_ref": "builtins.str"}}}, "GENERAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.GENERAL", "name": "GENERAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "general"}, "type_ref": "builtins.str"}}}, "INFRASTRUCTURE": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.INFRASTRUCTURE", "name": "INFRASTRUCTURE", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "infrastructure"}, "type_ref": "builtins.str"}}}, "REFACTORING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.REFACTORING", "name": "REFACTORING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "refactoring"}, "type_ref": "builtins.str"}}}, "SECURITY": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.SECURITY", "name": "SECURITY", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "security"}, "type_ref": "builtins.str"}}}, "TESTING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.TESTING", "name": "TESTING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "testing"}, "type_ref": "builtins.str"}}}, "UI_FRONTEND": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.ChangeType.UI_FRONTEND", "name": "UI_FRONTEND", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "ui_frontend"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_templates.ChangeType.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_templates.ChangeType", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Config": {".class": "SymbolTableNode", "cross_ref": "models.Config", "kind": "Gdef"}, "ContextAnalyzer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_templates.ContextAnalyzer", "name": "ContextAnalyzer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_templates.ContextAnalyzer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_templates", "mro": ["prompt_templates.ContextAnalyzer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.ContextAnalyzer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["prompt_templates.ContextAnalyzer", "models.Config"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ContextAnalyzer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_analyze_author_experience": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "author"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.ContextAnalyzer._analyze_author_experience", "name": "_analyze_author_experience", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "author"], "arg_types": ["prompt_templates.ContextAnalyzer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_analyze_author_experience of ContextAnalyzer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_determine_risk_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "change_type"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.ContextAnalyzer._determine_risk_context", "name": "_determine_risk_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "document", "change_type"], "arg_types": ["prompt_templates.ContextAnalyzer", "document_database.DocumentRecord", "prompt_templates.ChangeType"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_determine_risk_context of ContextAnalyzer", "ret_type": "prompt_templates.RiskContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_identify_config_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "changed_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.ContextAnalyzer._identify_config_files", "name": "_identify_config_files", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "changed_files"], "arg_types": ["prompt_templates.ContextAnalyzer", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_identify_config_files of ContextAnalyzer", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_identify_critical_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "changed_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.ContextAnalyzer._identify_critical_files", "name": "_identify_critical_files", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "changed_files"], "arg_types": ["prompt_templates.ContextAnalyzer", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_identify_critical_files of ContextAnalyzer", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_identify_test_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "changed_files"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.ContextAnalyzer._identify_test_files", "name": "_identify_test_files", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "changed_files"], "arg_types": ["prompt_templates.ContextAnalyzer", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_identify_test_files of ContextAnalyzer", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_hotfix": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "commit_message"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.ContextAnalyzer._is_hotfix", "name": "_is_hotfix", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "commit_message"], "arg_types": ["prompt_templates.ContextAnalyzer", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_hotfix of ContextAnalyzer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_maintenance_window": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.ContextAnalyzer._is_maintenance_window", "name": "_is_maintenance_window", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_templates.ContextAnalyzer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_maintenance_window of ContextAnalyzer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_is_pre_release_timing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.ContextAnalyzer._is_pre_release_timing", "name": "_is_pre_release_timing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["prompt_templates.ContextAnalyzer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_pre_release_timing of ContextAnalyzer", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "analyze_change_context": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "document", "changed_files", "commit_message", "diff_content"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.ContextAnalyzer.analyze_change_context", "name": "analyze_change_context", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1, 1], "arg_names": ["self", "document", "changed_files", "commit_message", "diff_content"], "arg_types": ["prompt_templates.ContextAnalyzer", "document_database.DocumentRecord", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_change_context of ContextAnalyzer", "ret_type": "prompt_templates.ChangeContext", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_templates.ContextAnalyzer.config", "name": "config", "type": "models.Config"}}, "helpers": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_templates.ContextAnalyzer.helpers", "name": "helpers", "type": "context_analyzer_helpers.ContextAnalyzerHelpers"}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_templates.ContextAnalyzer.logger", "name": "logger", "type": "logging.Logger"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_templates.ContextAnalyzer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_templates.ContextAnalyzer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "DocumentRecord": {".class": "SymbolTableNode", "cross_ref": "document_database.DocumentRecord", "kind": "Gdef"}, "Enum": {".class": "SymbolTableNode", "cross_ref": "enum.Enum", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "PromptTemplateManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_templates.PromptTemplateManager", "name": "PromptTemplateManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "prompt_templates.PromptTemplateManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "prompt_templates", "mro": ["prompt_templates.PromptTemplateManager", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.PromptTemplateManager.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "config"], "arg_types": ["prompt_templates.PromptTemplateManager", "models.Config"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of PromptTemplateManager", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_context_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.PromptTemplateManager._build_context_summary", "name": "_build_context_summary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["prompt_templates.PromptTemplateManager", "prompt_templates.ChangeContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_context_summary of PromptTemplateManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_metadata_system_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.PromptTemplateManager._build_metadata_system_prompt", "name": "_build_metadata_system_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["prompt_templates.PromptTemplateManager", "prompt_templates.ChangeContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_metadata_system_prompt of PromptTemplateManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_build_metadata_user_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.PromptTemplateManager._build_metadata_user_prompt", "name": "_build_metadata_user_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "context"], "arg_types": ["prompt_templates.PromptTemplateManager", "builtins.str", "prompt_templates.ChangeContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_build_metadata_user_prompt of PromptTemplateManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_code_review_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.PromptTemplateManager._get_code_review_criteria", "name": "_get_code_review_criteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["prompt_templates.PromptTemplateManager", "prompt_templates.ChangeContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_code_review_criteria of PromptTemplateManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_context_specific_guidance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.PromptTemplateManager._get_context_specific_guidance", "name": "_get_context_specific_guidance", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["prompt_templates.PromptTemplateManager", "prompt_templates.ChangeContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_context_specific_guidance of PromptTemplateManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_risk_assessment_criteria": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.PromptTemplateManager._get_risk_assessment_criteria", "name": "_get_risk_assessment_criteria", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "context"], "arg_types": ["prompt_templates.PromptTemplateManager", "prompt_templates.ChangeContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_risk_assessment_criteria of PromptTemplateManager", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_template_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "prompt_templates.PromptTemplateManager._template_cache", "name": "_template_cache", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "config": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_templates.PromptTemplateManager.config", "name": "config", "type": "models.Config"}}, "get_documentation_generation_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "commit_info", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.PromptTemplateManager.get_documentation_generation_prompt", "name": "get_documentation_generation_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "commit_info", "context"], "arg_types": ["prompt_templates.PromptTemplateManager", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "prompt_templates.ChangeContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_documentation_generation_prompt of PromptTemplateManager", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_metadata_extraction_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.PromptTemplateManager.get_metadata_extraction_prompt", "name": "get_metadata_extraction_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "content", "context"], "arg_types": ["prompt_templates.PromptTemplateManager", "builtins.str", "prompt_templates.ChangeContext"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_metadata_extraction_prompt of PromptTemplateManager", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_product_documentation_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "document", "technical_content", "context", "existing_docs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "prompt_templates.PromptTemplateManager.get_product_documentation_prompt", "name": "get_product_documentation_prompt", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 1], "arg_names": ["self", "document", "technical_content", "context", "existing_docs"], "arg_types": ["prompt_templates.PromptTemplateManager", "document_database.DocumentRecord", "builtins.str", "prompt_templates.ChangeContext", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_product_documentation_prompt of PromptTemplateManager", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "logger": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "prompt_templates.PromptTemplateManager.logger", "name": "logger", "type": "logging.Logger"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_templates.PromptTemplateManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_templates.PromptTemplateManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "RiskContext": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["enum.Enum"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "prompt_templates.RiskContext", "name": "RiskContext", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_enum"], "fullname": "prompt_templates.RiskContext", "has_param_spec_type": false, "metaclass_type": "enum.Enum<PERSON>", "metadata": {}, "module_name": "prompt_templates", "mro": ["prompt_templates.RiskContext", "enum.Enum", "builtins.object"], "names": {".class": "SymbolTable", "DEVELOPMENT": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.RiskContext.DEVELOPMENT", "name": "DEVELOPMENT", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "development"}, "type_ref": "builtins.str"}}}, "EXPERIMENTAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.RiskContext.EXPERIMENTAL", "name": "EXPERIMENTAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "experimental"}, "type_ref": "builtins.str"}}}, "PRODUCTION_CRITICAL": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.RiskContext.PRODUCTION_CRITICAL", "name": "PRODUCTION_CRITICAL", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "production_critical"}, "type_ref": "builtins.str"}}}, "PRODUCTION_STANDARD": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.RiskContext.PRODUCTION_STANDARD", "name": "PRODUCTION_STANDARD", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "production_standard"}, "type_ref": "builtins.str"}}}, "STAGING": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_final", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "prompt_templates.RiskContext.STAGING", "name": "STAGING", "type": {".class": "Instance", "args": [], "extra_attrs": null, "last_known_value": {".class": "LiteralType", "fallback": "builtins.str", "value": "staging"}, "type_ref": "builtins.str"}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "prompt_templates.RiskContext.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "prompt_templates.RiskContext", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tuple": {".class": "SymbolTableNode", "cross_ref": "typing.<PERSON>", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_templates.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_templates.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_templates.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_templates.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_templates.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "prompt_templates.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "C:\\home-repos\\reposense_ai\\prompt_templates.py"}